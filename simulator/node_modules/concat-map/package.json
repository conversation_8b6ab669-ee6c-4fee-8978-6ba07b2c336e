{"name": "concat-map", "description": "concatenative mapdashery", "version": "0.0.1", "repository": {"type": "git", "url": "git://github.com/substack/node-concat-map.git"}, "main": "index.js", "keywords": ["concat", "concatMap", "map", "functional", "higher-order"], "directories": {"example": "example", "test": "test"}, "scripts": {"test": "tape test/*.js"}, "devDependencies": {"tape": "~2.4.0"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "testling": {"files": "test/*.js", "browsers": {"ie": [6, 7, 8, 9], "ff": [3.5, 10, 15.0], "chrome": [10, 22], "safari": [5.1], "opera": [12]}}}