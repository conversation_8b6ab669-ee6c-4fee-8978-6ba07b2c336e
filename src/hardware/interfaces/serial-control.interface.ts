import { Observable } from 'rxjs';

export interface SerialControlService {
  // Room control commands
  turnOnLighting(): Promise<void>;
  turnOffLighting(): Promise<void>;
  openAccessLatch(): Promise<void>;
  closeAccessLatch(): Promise<void>;

  // Game control commands
  startArcades(durationMinutes: number): Promise<void>;
  stopArcades(): Promise<void>;
  stopTimers(): Promise<void>;

  // Display commands
  displayInstructions(instructions: string): Promise<void>;
  sendTeamData(teamData: any): Promise<void>;
  showWin(): Promise<void>;
  showLoss(): Promise<void>;
  showJackpotAnimation(): Promise<void>;
  celebrate(): Promise<void>;

  // Access control
  sendAccessDenied(): Promise<void>;

  // Event monitoring
  onRoomTimerExpired(): Observable<void>;
  onAllGamesComplete(): Observable<void>;
  onScoresReceived(): Observable<GameScores>;

  // Direct hardware control (optional - for advanced use)
  setOutput?(pin: number, value: 0 | 1): Promise<void>;
  getInputs?(): Promise<number>;
  openDoor?(durationMs?: number): Promise<void>;
  turnOnGreenIndicator?(): Promise<void>;
  turnOnRedIndicator?(): Promise<void>;
}

export interface GameScores {
  player1: number; // Score from arcade machine 1 (for player 1)
  player2: number; // Score from arcade machine 2 (for player 2)
  player3: number; // Score from arcade machine 3 (for player 3)
  player4: number; // Score from arcade machine 4 (for player 4)
}

// Hardware pin mapping (based on your working JS script)
export const CONTROLLINO_OUTPUTS = {
  DOOR: '05',
  LED_DOOR_GREEN: '02',
  LED_DOOR_RED: '01',
  LED_EFFECT: '04',
  POWER_CELL: '99',
} as const;

export const OUTPUT_STATE = {
  ON: '1',
  OFF: '0',
} as const;

export enum SerialCommand {
  // Direct hardware commands (format: O + pin + state)
  TURN_ON_LIGHTING = 'O991',     // Power cell ON
  TURN_OFF_LIGHTING = 'O990',    // Power cell OFF
  OPEN_ACCESS = 'O050',          // Door OFF (open)
  CLOSE_ACCESS = 'O051',         // Door ON (closed)

  // LED commands
  GREEN_LED_ON = 'O021',         // Green LED ON
  GREEN_LED_OFF = 'O020',        // Green LED OFF
  RED_LED_ON = 'O011',           // Red LED ON
  RED_LED_OFF = 'O010',          // Red LED OFF
  EFFECT_LED_ON = 'O041',        // Effect LED ON
  EFFECT_LED_OFF = 'O040',       // Effect LED OFF

  // Input query command
  GET_INPUTS = 'I',

  // High-level game commands (these might need custom handling)
  START_ARCADES = 'START_ARCADES',
  STOP_ARCADES = 'STOP_ARCADES',
  STOP_TIMERS = 'STOP_TIMERS',
  ACCESS_DENIED = 'ACCESS_DENIED',
  SHOW_WIN = 'SHOW_WIN',
  SHOW_LOSS = 'SHOW_LOSS',
  JACKPOT_ANIMATION = 'JACKPOT_ANIMATION',
  CELEBRATE = 'CELEBRATE',
  DISPLAY_INSTRUCTIONS = 'DISPLAY_INSTRUCTIONS',
  TEAM_DATA = 'TEAM_DATA',
}

export enum SerialEvent {
  ROOM_TIMER_EXPIRED = 'ROOM_TIMER_EXPIRED',
  ALL_GAMES_COMPLETE = 'ALL_GAMES_COMPLETE',
  SCORES_RECEIVED = 'SCORES_RECEIVED',
}

// Utility functions to create hardware commands dynamically (like your JS script)
export class ControllinoCommands {
  /**
   * Create an output command (like your set_output function)
   * @param pin Output pin number (1-99)
   * @param value Output state (0 or 1)
   * @returns Command string in format "Oxxv" (e.g., "O051")
   */
  static setOutput(pin: number, value: 0 | 1): string {
    const pinStr = pin.toString().padStart(2, '0');
    return `O${pinStr}${value}`;
  }

  /**
   * Create an input query command
   * @returns "I" command to get all input states
   */
  static getInputs(): string {
    return 'I';
  }

  /**
   * Create a bar LED command (like your setBarled function)
   * @param value LED bar level (1-100)
   * @returns Command string for LED bar
   */
  static setBarLed(value: number): string {
    const level = Math.max(1, Math.min(100, value)); // Clamp between 1-100
    return `L01${String.fromCharCode(level)}`;
  }
}
