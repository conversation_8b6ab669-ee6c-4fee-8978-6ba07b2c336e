import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Observable, Subject } from 'rxjs';
import {
  SerialControlService,
  GameScores,
  SerialCommand,
  SerialEvent,
} from '../interfaces/serial-control.interface';

// Import SerialPort for version 9.x
const SerialPort = require('serialport');

@Injectable()
export class ControllinoSerialService implements SerialControlService {
  private serialPort: any;
  private roomTimerExpired$ = new Subject<void>();
  private allGamesComplete$ = new Subject<void>();
  private scoresReceived$ = new Subject<GameScores>();

  constructor(private cfg: ConfigService) {
    this.initializeSerial();
  }

  private initializeSerial() {
    const portPath = this.cfg.get<string>(
      'global.hardware.controllino.serial.defaultPort',
      '/dev/ttyACM0',
    );
    const baudRate = this.cfg.get<number>(
      'global.hardware.controllino.serial.baudRate',
      9600,
    );

    // For serialport v9.x, use the constructor with path and options
    this.serialPort = new SerialPort(portPath, {
      baudRate: baudRate,
    });

    this.serialPort.on('data', (data: Buffer) => {
      this.handleSerialData(data.toString());
    });

    this.serialPort.on('error', (error: Error) => {
      console.error(`❌ Serial port error: ${error.message}`);
    });

    console.log(`Serial port initialized on ${portPath} at ${baudRate} baud`);
  }

  private handleSerialData(data: string) {
    const trimmedData = data.trim();
    // Debug: console.log(`📡 Received serial data: ${trimmedData}`);

    try {
      // Handle different types of incoming data
      if (trimmedData === SerialEvent.ROOM_TIMER_EXPIRED) {
        console.log('🕐 Room timer expired event received');
        this.roomTimerExpired$.next();
      } else if (trimmedData === SerialEvent.ALL_GAMES_COMPLETE) {
        console.log('🎮 All games complete event received');
        this.allGamesComplete$.next();
      } else if (trimmedData.startsWith(SerialEvent.SCORES_RECEIVED)) {
        this.handleScoresData(trimmedData);
      }
    } catch (error) {
      console.error(
        `❌ Error parsing serial data: ${
          error instanceof Error ? error.message : String(error)
        }`,
      );
    }
  }

  private handleScoresData(data: string) {
    try {
      // Expected format: "SCORES_RECEIVED:{ player1: 100, player2: 200, player3: 0, player4: 150 }"
      const jsonPart = data.substring(data.indexOf(':') + 1);
      const scores: GameScores = JSON.parse(jsonPart);

      console.log(`📊 Player scores received: ${JSON.stringify(scores)}`);
      this.scoresReceived$.next(scores);
    } catch (error) {
      console.error(
        `❌ Error parsing scores data: ${
          error instanceof Error ? error.message : String(error)
        }`,
      );
    }
  }

  private async sendCommand(
    command: SerialCommand,
    parameter?: string | number,
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      const commandString = parameter ? `${command}:${parameter}` : command;

      this.serialPort.write(
        `${commandString}\n`,
        (error: Error | null | undefined) => {
          if (error) {
            console.error(
              `❌ Failed to send command ${commandString}: ${error.message}`,
            );
            reject(error);
          } else {
            // Debug: console.log(`📤 Sent command: ${commandString}`);
            resolve();
          }
        },
      );
    });
  }

  // Room control commands
  async turnOnLighting(): Promise<void> {
    await this.sendCommand(SerialCommand.TURN_ON_LIGHTING);
  }

  async turnOffLighting(): Promise<void> {
    await this.sendCommand(SerialCommand.TURN_OFF_LIGHTING);
  }

  async openAccessLatch(): Promise<void> {
    await this.sendCommand(SerialCommand.OPEN_ACCESS);
  }

  async closeAccessLatch(): Promise<void> {
    await this.sendCommand(SerialCommand.CLOSE_ACCESS);
  }

  // Game control commands
  async startArcades(
    maxGames: number,
    durationMinutes?: number,
  ): Promise<void> {
    const params = durationMinutes
      ? `${maxGames}:${durationMinutes}`
      : maxGames;
    await this.sendCommand(SerialCommand.START_ARCADES, params);
  }

  async stopArcades(): Promise<void> {
    await this.sendCommand(SerialCommand.STOP_ARCADES);
  }

  async stopTimers(): Promise<void> {
    await this.sendCommand(SerialCommand.STOP_TIMERS);
  }

  // Display commands
  async displayInstructions(instructions: string): Promise<void> {
    await this.sendCommand(SerialCommand.DISPLAY_INSTRUCTIONS, instructions);
  }

  async sendTeamData(teamData: any): Promise<void> {
    await this.sendCommand(SerialCommand.TEAM_DATA, JSON.stringify(teamData));
  }

  async showWin(): Promise<void> {
    await this.sendCommand(SerialCommand.SHOW_WIN);
  }

  async showLoss(): Promise<void> {
    await this.sendCommand(SerialCommand.SHOW_LOSS);
  }

  async showJackpotAnimation(): Promise<void> {
    await this.sendCommand(SerialCommand.JACKPOT_ANIMATION);
  }

  async celebrate(): Promise<void> {
    await this.sendCommand(SerialCommand.CELEBRATE);
  }

  // Access control
  async sendAccessDenied(): Promise<void> {
    await this.sendCommand(SerialCommand.ACCESS_DENIED);
  }

  // Event monitoring
  onRoomTimerExpired(): Observable<void> {
    return this.roomTimerExpired$.asObservable();
  }

  onAllGamesComplete(): Observable<void> {
    return this.allGamesComplete$.asObservable();
  }

  onScoresReceived(): Observable<GameScores> {
    return this.scoresReceived$.asObservable();
  }

  // Direct hardware control methods (like your JS script)

  /**
   * Set output pin state directly (like your set_output function)
   * @param pin Output pin number (1-99)
   * @param value Output state (0 or 1)
   */
  async setOutput(pin: number, value: 0 | 1): Promise<void> {
    const pinStr = pin.toString().padStart(2, '0');
    const command = `O${pinStr}${value}`;
    await this.sendCommand(command as any); // Cast to bypass enum restriction
  }

  /**
   * Get all input states (like your get_input1 function)
   * @returns Promise that resolves with input mask value
   */
  async getInputs(): Promise<number> {
    // This would need to be implemented with proper response handling
    // For now, just send the command
    await this.sendCommand(SerialCommand.GET_INPUTS as any);
    return 0; // Placeholder - would need proper response parsing
  }

  /**
   * Control door directly (like your openDoor function)
   * @param durationMs How long to keep door open (default 10 seconds)
   */
  async openDoor(durationMs: number = 10000): Promise<void> {
    console.log(`🚪 Opening door for ${durationMs}ms`);

    // Open door (turn off door output)
    await this.setOutput(5, 0); // OUTPUT_DOOR = 5, OFF = 0

    // Close door after duration
    setTimeout(async () => {
      await this.setOutput(5, 1); // OUTPUT_DOOR = 5, ON = 1
      console.log('🚪 Door closed automatically');
    }, durationMs);
  }

  /**
   * Control green indicator LED (like your turnOnGreenIndicator function)
   */
  async turnOnGreenIndicator(): Promise<void> {
    console.log('💚 Turning on green indicator');
    await this.setOutput(2, 1); // OUTPUT_LED_DOOR_GREEN = 2, ON = 1
    await this.setOutput(1, 0); // OUTPUT_LED_DOOR_RED = 1, OFF = 0
  }

  /**
   * Control red indicator LED (like your turnOffGreenIndicator function)
   */
  async turnOnRedIndicator(): Promise<void> {
    console.log('❤️ Turning on red indicator');
    await this.setOutput(1, 1); // OUTPUT_LED_DOOR_RED = 1, ON = 1
    await this.setOutput(2, 0); // OUTPUT_LED_DOOR_GREEN = 2, OFF = 0
  }
}
